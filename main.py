import os
import json
import logging
from datetime import datetime, timedelta
import sys
import pytz
from pymongo import MongoClient
from dotenv import load_dotenv
from flask import Flask
from threading import Thread
from telegram import Update, InlineKeyboardMarkup, InlineKeyboardButton
from telegram.constants import ParseMode
from telegram.ext import (
    ApplicationBuilder, CommandHandler, MessageHandler, filters, ContextTypes, ConversationHandler,
    JobQueue, CallbackQueryHandler
)
import re
import time
import aiohttp
from telegram.error import NetworkError, TimedOut, RetryAfter

# Load environment variables
load_dotenv()
BOT_TOKEN = os.getenv("BOT_TOKEN")
MONGO_URI = os.getenv("MONGO_URI")
OWNER_ID = 1051790413  # Your Telegram ID
IST = pytz.timezone("Asia/Kolkata")

# Helper function to properly convert any value to boolean
def to_boolean(value):
    """
    Convert various data types to boolean
    
    Args:
        value: Value to convert - can be None, string, int, or bool
        
    Returns:
        bool: True or False based on the value
    """
    if value is None:
        return False
    
    if isinstance(value, bool):
        return value
    
    if isinstance(value, int):
        return bool(value)
    
    if isinstance(value, str):
        # Handle common string representations
        return value.lower() in ('true', 'yes', 'y', '1', 'on')
    
    # For any other type, use Python's bool conversion
    return bool(value)

# Global aiohttp session
http_session = None

# MongoDB setup with connection pooling
client = MongoClient(MONGO_URI, 
                    maxPoolSize=50,
                    connectTimeoutMS=5000,
                    socketTimeoutMS=30000,
                    serverSelectionTimeoutMS=5000)
db = client["anime_schedule"]
reminders_collection = db["reminders"]
users_collection = db["users"]  # New collection to store user information

# Logger setup with file handler
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("bot.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Flask web server for UptimeRobot
app = Flask(__name__)

@app.route("/")
def home():
    return {"status": "Bot is running!"}, 200

# Function to run the Flask app in a separate thread
def run_web_server():
    app.run(host="0.0.0.0", port=int(os.getenv("PORT", 8080)))

# Restart function
def restart_bot():
    """Restarts the bot process."""
    try:
        logger.info("Restarting bot...")
        os.execv(sys.executable, [sys.executable] + sys.argv)
    except Exception as e:
        logger.exception("Failed to restart bot.")

# States for ConversationHandler
ADD_SCHEDULE_JSON, REMOVE_TITLE, REMOVE_CONFIRMATION, SEARCH_RESULT_SELECTION, TIMEZONE_SELECTION = range(5)

async def is_owner(update: Update):
    return update.effective_user and update.effective_user.id == OWNER_ID

# User registration and management
async def register_user(update: Update):
    """Register a new user or update existing user in the database"""
    user = update.effective_user
    if not user:
        return False
        
    user_data = {
        "user_id": user.id,
        "username": user.username,
        "first_name": user.first_name,
        "last_name": user.last_name,
        "last_active": datetime.utcnow(),
        "use_delay": True,  # Default setting - 2 hour delay enabled - ensure this is a boolean True
        "timezone": "Asia/Kolkata",  # Default timezone - IST
    }
    
    # Update user but don't overwrite existing delay setting if it exists
    existing_user = users_collection.find_one({"user_id": user.id})
    
    if existing_user and "use_delay" in existing_user:
        # Skip setting use_delay if already exists
        del user_data["use_delay"]
    
    users_collection.update_one(
        {"user_id": user.id},
        {"$set": user_data},
        upsert=True
    )
    return True

# Helper function to get user's timezone
async def get_user_timezone(user_id):
    user = users_collection.find_one({"user_id": user_id})
    if user and "timezone" in user:
        return pytz.timezone(user["timezone"])
    return IST  # Default to IST if not found

# Command to set user's timezone
async def set_timezone(update: Update, context: ContextTypes.DEFAULT_TYPE):
    # Handle both direct command and callback query
    if update.callback_query:
        query = update.callback_query
        await query.answer()
        
        # Create a new message for the conversation handler
        message = await context.bot.send_message(
            chat_id=query.message.chat_id,
            text="Let's set up your timezone..."
        )
        
        # Create a dummy update object with the new message
        from telegram._update import Update as UpdateObj
        from telegram._message import Message
        
        # Create a copy of the update with modified message
        update_dict = update.to_dict()
        update_dict["message"] = message.to_dict()
        update_dict["callback_query"] = None
        new_update = UpdateObj.de_json(update_dict, context.bot)
        
        # Call the function recursively with the new update
        return await set_timezone(new_update, context)
    
    # Register the user if not registered
    if not await register_user(update):
        await update.message.reply_text("An error occurred. Please try again.")
        return ConversationHandler.END
    
    user_id = update.effective_user.id
    
    # Get current timezone
    user = users_collection.find_one({"user_id": user_id})
    current_tz = user.get("timezone", "Asia/Kolkata") if user else "Asia/Kolkata"
    
    # List of common timezones by region
    common_timezones = {
        "Asia": ["Asia/Kolkata", "Asia/Tokyo", "Asia/Singapore", "Asia/Dubai", "Asia/Bangkok"],
        "Europe": ["Europe/London", "Europe/Paris", "Europe/Berlin", "Europe/Moscow"],
        "America": ["America/New_York", "America/Chicago", "America/Denver", "America/Los_Angeles"],
        "Australia": ["Australia/Sydney", "Australia/Perth"],
        "Africa": ["Africa/Cairo", "Africa/Johannesburg"],
        "Pacific": ["Pacific/Auckland", "Pacific/Honolulu"]
    }
    
    # Store the timezone data in user_data for later use
    context.user_data["timezones"] = common_timezones
    
    # Create a keyboard with buttons for each region
    regions = list(common_timezones.keys())
    context.user_data["regions"] = regions
    
    # Create message and inline keyboard
    response = f"Your current timezone is: {current_tz}\n\nPlease select your region to change your timezone:"
    
    # Create inline keyboard for region selection
    keyboard = []
    for region in regions:
        keyboard.append([InlineKeyboardButton(region, callback_data=f"region_{region}")])
    
    # Add an "Other" button for manual entry
    keyboard.append([InlineKeyboardButton("Other (Manual Entry)", callback_data="other_timezone")])
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    await update.message.reply_text(response, reply_markup=reply_markup)
    return TIMEZONE_SELECTION

# Handle timezone selection
async def process_timezone_selection(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_response = update.message.text.strip()
    
    # If user sent "other", let them enter a timezone directly
    if user_response.lower() == "other":
        await update.message.reply_text(
            "Please enter your timezone in format 'Continent/City'.\n"
            "Examples: Asia/Tokyo, Europe/London, America/New_York\n\n"
            "You can find the full list here: https://en.wikipedia.org/wiki/List_of_tz_database_time_zones"
        )
        context.user_data["timezone_step"] = "manual"
        return TIMEZONE_SELECTION
    
    # If we're in the manual entry step
    if context.user_data.get("timezone_step") == "manual":
        try:
            # Try to create a timezone object to validate
            timezone = pytz.timezone(user_response)
            
            # Get current timezone to check if this is a change
            user_id = update.effective_user.id
            user = users_collection.find_one({"user_id": user_id})
            current_tz = user.get("timezone", "Asia/Kolkata") if user else "Asia/Kolkata"
            changing_timezone = current_tz != user_response and user.get("timezone_set_manually", False)
            
            # Update user's timezone
            users_collection.update_one(
                {"user_id": update.effective_user.id},
                {"$set": {
                    "timezone": user_response,
                    "timezone_set_manually": True  # Mark that user manually set their timezone
                }}
            )
            
            # Get current time in the selected timezone
            now = datetime.now(timezone)
            time_str = now.strftime("%d/%m/%Y %H:%M")
            
            # Different message if changing timezone vs setting for first time
            if changing_timezone:
                success_message = (
                    f"✅ Timezone changed from {current_tz} to {user_response}.\n"
                    f"Current time in your timezone: {time_str}\n\n"
                    "Your reminders will now be adjusted according to your new timezone."
                )
            else:
                success_message = (
                    f"✅ Timezone set to {user_response}.\n"
                    f"Current time in your timezone: {time_str}\n\n"
                    "Your reminders will now be adjusted according to your local time."
                )
            
            await update.message.reply_text(success_message)
            
            # Clean up user_data
            if "timezones" in context.user_data:
                del context.user_data["timezones"]
            if "regions" in context.user_data:
                del context.user_data["regions"]
            if "timezone_step" in context.user_data:
                del context.user_data["timezone_step"]
            if "selected_region" in context.user_data:
                del context.user_data["selected_region"]
                
            return ConversationHandler.END
            
        except pytz.exceptions.UnknownTimeZoneError:
            await update.message.reply_text(
                "❌ Invalid timezone format. Please enter a valid timezone in format 'Continent/City'.\n"
                "Examples: Asia/Tokyo, Europe/London, America/New_York\n\n"
                "You can find the full list here: https://en.wikipedia.org/wiki/List_of_tz_database_time_zones"
            )
            return TIMEZONE_SELECTION
    
    # If we're in the timezone selection step (after region was selected)
    if context.user_data.get("timezone_step") == "timezone":
        try:
            selection = int(user_response)
            region = context.user_data.get("selected_region")
            timezones = context.user_data.get("timezones", {}).get(region, [])
            
            # Validate selection
            if selection < 1 or selection > len(timezones):
                await update.message.reply_text("Invalid selection. Please try again.")
                return TIMEZONE_SELECTION
            
            # Get selected timezone
            timezone_str = timezones[selection - 1]
            
            # Update user's timezone
            users_collection.update_one(
                {"user_id": update.effective_user.id},
                {"$set": {
                    "timezone": timezone_str,
                    "timezone_set_manually": True  # Mark that user manually set their timezone
                }}
            )
            
            # Get current time in the selected timezone
            timezone = pytz.timezone(timezone_str)
            now = datetime.now(timezone)
            time_str = now.strftime("%d/%m/%Y %H:%M")
            
            await update.message.reply_text(
                f"✅ Timezone set to {timezone_str}.\n"
                f"Current time in your timezone: {time_str}\n\n"
                "Your reminders will now be adjusted according to your local time."
            )
            
            # Clean up user_data
            if "timezones" in context.user_data:
                del context.user_data["timezones"]
            if "regions" in context.user_data:
                del context.user_data["regions"]
            if "timezone_step" in context.user_data:
                del context.user_data["timezone_step"]
            if "selected_region" in context.user_data:
                del context.user_data["selected_region"]
                
            return ConversationHandler.END
            
        except ValueError:
            await update.message.reply_text("Please enter a valid number.")
            return TIMEZONE_SELECTION
        except Exception as e:
            logger.error(f"Error in timezone selection: {str(e)}")
            await update.message.reply_text("An error occurred. Please try again.")
            return TIMEZONE_SELECTION
    
    # If we're in the region selection step (initial step)
    try:
        selection = int(user_response)
        regions = context.user_data.get("regions", [])
        
        # Validate selection
        if selection < 1 or selection > len(regions):
            await update.message.reply_text("Invalid selection. Please try again.")
            return TIMEZONE_SELECTION
        
        # Get selected region
        region = regions[selection - 1]
        context.user_data["selected_region"] = region
        
        # Show timezones for the selected region
        timezones = context.user_data.get("timezones", {}).get(region, [])
        
        response = f"Select a timezone for {region} by sending its number:\n\n"
        for i, tz in enumerate(timezones, 1):
            # Get current time in this timezone
            now = datetime.now(pytz.timezone(tz))
            time_str = now.strftime("%H:%M")
            response += f"{i}. {tz} (Current time: {time_str})\n"
        
        await update.message.reply_text(response)
        context.user_data["timezone_step"] = "timezone"
        return TIMEZONE_SELECTION
        
    except ValueError:
        await update.message.reply_text("Please enter a valid number or 'other'.")
        return TIMEZONE_SELECTION
    except Exception as e:
        logger.error(f"Error in timezone selection: {str(e)}")
        await update.message.reply_text("An error occurred. Please try again.")
        return TIMEZONE_SELECTION

# Admin function to get user count
async def get_user_count():
    """Get the total count of users in the database"""
    return users_collection.count_documents({})

# Helper to process missed reminders
async def process_missed_reminders(context: ContextTypes.DEFAULT_TYPE):
    now = datetime.utcnow().replace(tzinfo=pytz.UTC)
    missed_reminders = reminders_collection.find({"reminder_time": {"$lte": now}})
    for reminder in missed_reminders:
        user_id = reminder.get('user_id', OWNER_ID) # Default to owner for legacy reminders
        
        if reminder.get('is_custom', False):
            # Handle missed custom reminders
            task = reminder.get('task', 'No task description')
            reminder_time = reminder['reminder_time'].astimezone(IST).strftime('%d/%m/%Y %H:%M')
            
            await context.bot.send_message(
                user_id,
                f"Missed Anime Reminder: {task}\nTime: {reminder_time} IST"
            )
        else:
            # Handle missed anime reminders
            await context.bot.send_message(
                user_id,
                f"Missed Reminder: {reminder['title']} Episode {reminder['episode']} aired at "
                f"{reminder['airing_time'].astimezone(IST).strftime('%d/%m/%Y %H:%M')} IST."
            )
        reminders_collection.delete_one({"_id": reminder["_id"]})

# Reminder scheduler
async def send_reminders(context: ContextTypes.DEFAULT_TYPE):
    now = datetime.utcnow().replace(tzinfo=pytz.UTC)
    reminders = reminders_collection.find({"reminder_time": {"$lte": now}})
    for reminder in reminders:
        user_id = reminder.get('user_id', OWNER_ID) # Default to owner for legacy reminders
        
        try:
            # Get user's timezone
            user_timezone = await get_user_timezone(user_id)
            
            # Check if it's a custom reminder
            if reminder.get('is_custom', False):
                # Send custom reminder message
                task = reminder.get('task', 'No task description')
                reminder_time = reminder['reminder_time'].astimezone(user_timezone).strftime('%d/%m/%Y %H:%M')
                
                await context.bot.send_message(
                    user_id,
                    f"⏰ ANIME REMINDER: {task}\nTime: {reminder_time} ({user_timezone.zone})"
                )
            else:
                # Format the title with backticks for Markdown
                title = f"`{reminder['title']}`"
                url = reminder.get('url', 'No URL provided')
                airing_time = reminder['airing_time'].astimezone(user_timezone).strftime('%d/%m/%Y %H:%M')

                # Send the reminder message
                await context.bot.send_message(
                    user_id,
                    f"Reminder: {title} Episode {reminder['episode']} airs at {airing_time} ({user_timezone.zone}).\n\nLink: {url}",
                    parse_mode="Markdown"
                )
            # Remove the reminder from the database
            reminders_collection.delete_one({"_id": reminder["_id"]})
        except Exception as e:
            logger.error(f"Error sending reminder to user {user_id}: {str(e)}")
            # Don't delete the reminder if there was an error sending it
            continue

# Command: /start
async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    # Register the user
    await register_user(update)
    
    user_id = update.effective_user.id
    
    # Get user info
    user = users_collection.find_one({"user_id": user_id})
    timezone_set = user and "timezone_set_manually" in user and user["timezone_set_manually"]
    
    # Create inline keyboard markup for timezone setting
    timezone_button = InlineKeyboardButton("⏰ Set your timezone", callback_data="set_timezone")
    help_button = InlineKeyboardButton("📖 How to use", callback_data="show_help")
    markup = InlineKeyboardMarkup([[timezone_button], [help_button]])
    
    if await is_owner(update):
        await update.message.reply_text(
            "Welcome Admin! You have access to special commands:\n\n"
            "Regular Commands:\n"
            "/addschedule - Add anime schedule from JSON\n"
            "/show - Show all your reminders\n"
            "/next - Show your upcoming reminders\n"
            "/remove - Remove your reminders by title\n"
            "/delay - Toggle 2-hour reminder delay (ON/OFF)\n"
            "/timezone - Set your local timezone\n\n"
            "Admin Commands:\n"
            "/usercount - Show total number of users\n"
            "/broadcast - Reply to a message with this command to broadcast it to all users"
        )
    else:
        welcome_msg = (
            "Welcome to Anime Reminder Bot! 📺✨\n\n"
            "Use these commands to manage your anime reminders:\n\n"
            "/addschedule - Add anime schedule from JSON\n"
            "/show - Show all your reminders\n"
            "/next - Show your upcoming reminders\n"
            "/remove - Remove your reminders by title\n"
            "/delay - Toggle 2-hour reminder delay (ON/OFF)\n"
            "/timezone - Set your local timezone\n\n"
            "To create your anime schedule:\n\n"
            "1. First, download the Schedule Maker HTML file using Oshi bot:\n"
            "   🔗 https://t.me/Oshi_animeguru_bot?start=Z2V0LTkxMDkzMDQ2MTMwMzE2ODA\n\n"
            "2. Open the HTML file in any web browser (no installation needed)\n"
            "3. Select the anime you want to track\n"
            "4. Generate and download the schedule as JSON\n"
            "5. Send the JSON file to this bot with the /addschedule command\n"
            "6. Your reminders will be set automatically!\n\n"
            "📝 Note: By default, reminders are sent 2 hours AFTER episodes air.\n"
            "This ensures episodes are available for streaming. Use /delay to change this."
        )
        
        # Add timezone warning if user hasn't set timezone manually
        if not timezone_set:
            welcome_msg += "\n\n⚠️ Important: Please set your timezone for accurate reminders!"
        
        await update.message.reply_text(welcome_msg, reply_markup=markup)

# Callback query handler for inline buttons
async def button_handler(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Handle button presses from inline keyboards"""
    query = update.callback_query
    await query.answer()  # Answer the callback query
    
    if query.data == "set_timezone":
        # Remove inline keyboard from this message
        await query.edit_message_reply_markup(None)
        await set_timezone(update, context)
        
    elif query.data == "other_timezone":
        # Handle manual timezone entry
        await query.edit_message_text(
            "Please enter your timezone in format 'Continent/City'.\n"
            "Examples: Asia/Tokyo, Europe/London, America/New_York\n\n"
            "You can find the full list here: https://en.wikipedia.org/wiki/List_of_tz_database_time_zones"
        )
        # Set the timezone step to manual
        context.user_data["timezone_step"] = "manual"
        return TIMEZONE_SELECTION
        
    elif query.data == "back_to_regions":
        # Go back to region selection
        # Get current timezone
        user_id = update.effective_user.id
        user = users_collection.find_one({"user_id": user_id})
        current_tz = user.get("timezone", "Asia/Kolkata") if user else "Asia/Kolkata"
        
        # Use the common_timezones from user_data or recreate them
        common_timezones = context.user_data.get("timezones", {})
        if not common_timezones:
            common_timezones = {
                "Asia": ["Asia/Kolkata", "Asia/Tokyo", "Asia/Singapore", "Asia/Dubai", "Asia/Bangkok"],
                "Europe": ["Europe/London", "Europe/Paris", "Europe/Berlin", "Europe/Moscow"],
                "America": ["America/New_York", "America/Chicago", "America/Denver", "America/Los_Angeles"],
                "Australia": ["Australia/Sydney", "Australia/Perth"],
                "Africa": ["Africa/Cairo", "Africa/Johannesburg"],
                "Pacific": ["Pacific/Auckland", "Pacific/Honolulu"]
            }
            context.user_data["timezones"] = common_timezones
        
        regions = list(common_timezones.keys())
        context.user_data["regions"] = regions
        
        # Create message and inline keyboard
        response = f"Your current timezone is: {current_tz}\n\nPlease select your region:"
        
        # Create inline keyboard for region selection
        keyboard = []
        for region in regions:
            keyboard.append([InlineKeyboardButton(region, callback_data=f"region_{region}")])
        
        # Add an "Other" button for manual entry
        keyboard.append([InlineKeyboardButton("Other (Manual Entry)", callback_data="other_timezone")])
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        # Try to edit the message, or send a new one if it fails
        try:
            await query.edit_message_text(response, reply_markup=reply_markup)
        except Exception as e:
            logger.error(f"Error going back to regions: {e}")
            try:
                await context.bot.send_message(
                    chat_id=query.message.chat_id,
                    text=response,
                    reply_markup=reply_markup
                )
            except Exception as nested_e:
                logger.error(f"Failed to send new region selection message: {nested_e}")
        
        return TIMEZONE_SELECTION
        
    elif query.data == "show_help":
        help_text = (
            "📋 *Bot Commands:*\n\n"
            "*Setup Commands:*\n"
            "/addschedule - Add a new anime reminder\n"
            "/timezone - Set your timezone\n\n"
            "*Viewing Commands:*\n"
            "/show - Show all your scheduled reminders\n"
            "/next - Show your next reminders\n\n"
            "*Management Commands:*\n"
            "/remove - Remove a scheduled reminder\n"
            "/delay - Toggle 2-hour reminder delay\n\n"
            "*How to use the bot:*\n"
            "1. First set your timezone with /timezone\n"
            "2. Add your anime reminders with /addschedule\n"
            "3. You'll receive notifications when episodes air\n"
            "4. Use /delay to toggle whether reminders are sent exactly at airing time or 2 hours later\n"
        )
        
        keyboard = [
            [InlineKeyboardButton("⬅️ Back to Start", callback_data="back_to_start")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            text=help_text,
            reply_markup=reply_markup,
            parse_mode=ParseMode.MARKDOWN
        )
        
    elif query.data == "back_to_start":
        # Generate the start menu again
        user_id = update.effective_user.id
        user = users_collection.find_one({"user_id": user_id})
        timezone_warning = ""
        
        if not user or not user.get("timezone") or not user.get("timezone_set_manually", False):
            timezone_warning = "⚠️ *Warning:* Your timezone is not set! Set it for accurate reminders.\n\n"
        
        welcome_text = (
            f"👋 Welcome to the Anime Reminder Bot!\n\n"
            f"{timezone_warning}"
            "This bot helps you keep track of your anime schedule and sends you reminders when new episodes air."
        )
        
        keyboard = [
            [InlineKeyboardButton("🕒 Set your timezone", callback_data="set_timezone")],
            [InlineKeyboardButton("❓ Help", callback_data="show_help")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await query.edit_message_text(
            text=welcome_text,
            reply_markup=reply_markup,
            parse_mode=ParseMode.MARKDOWN
        )
    
    # Add any additional button handlers here for future use
    else:
        # Process region selection if data starts with "region_"
        if query.data.startswith("region_"):
            try:
                await process_timezone_region_selection(update, context)
            except Exception as e:
                logger.error(f"Error in region selection: {e}")
                # Send a new message as a fallback
                await query.message.reply_text("There was an error processing your selection. Please try using the /timezone command.")
        
        # Process timezone selection if data starts with "timezone_"
        elif query.data.startswith("timezone_"):
            try:
                # Extract the selection number
                selection_str = query.data.split("_")[1]
                context.user_data["timezone_selection"] = int(selection_str)
                
                # Log the selection for debugging
                logger.info(f"Timezone selection: {selection_str} (for region: {context.user_data.get('selected_region')})")
                
                # Send status message in case timezone selection takes time
                await query.answer("Setting timezone...")
                
                # Call the appropriate handler
                await process_timezone_list_selection(update, context)
            except ValueError as ve:
                logger.error(f"Invalid timezone selection number format: {ve}")
                await query.message.reply_text("Invalid timezone selection format. Please try using the /timezone command.")
            except Exception as e:
                logger.error(f"Error processing timezone selection: {str(e)}")
                await query.message.reply_text("An error occurred processing your timezone selection. Please try using the /timezone command instead.")

# Command: /addschedule
async def add_schedule(update: Update, context: ContextTypes.DEFAULT_TYPE):
    # Register the user
    if not await register_user(update):
        await update.message.reply_text("An error occurred. Please try again.")
        return ConversationHandler.END
    
    # Guide users about how to get the JSON file    
    await update.message.reply_text(
        "Please upload a JSON file with your anime schedule.\n\n"
        "To create your schedule:\n\n"
        "1. Download the Schedule Maker HTML file using Oshi bot:\n"
        "   https://t.me/Oshi_animeguru_bot?start=Z2V0LTkxMDkzMDQ2MTMwMzE2ODA\n\n"
        "2. Open the HTML file in any web browser (no installation needed)\n"
        "3. Select the anime you want to track\n" 
        "4. Click the 'Download JSON' button\n"
        "5. Send the downloaded JSON file here\n\n"
        "I'll set up reminders for all episodes in your schedule!"
    )
    return ADD_SCHEDULE_JSON

async def handle_schedule_json(update: Update, context: ContextTypes.DEFAULT_TYPE):
    # Register the user
    if not await register_user(update):
        await update.message.reply_text("An error occurred. Please try again.")
        return ConversationHandler.END

    try:
        # Get user ID
        user_id = update.effective_user.id
        
        # Get user preferences
        user = users_collection.find_one({"user_id": user_id})
        use_delay = to_boolean(user.get("use_delay", True)) if user else True
        
        # Get user's timezone
        user_timezone = await get_user_timezone(user_id)
        
        # Get file from message
        document = update.message.document
        file = await context.bot.get_file(document.file_id)
        file_content = await file.download_as_bytearray()
        schedule_data = json.loads(file_content.decode("utf-8"))

        # Process the schedule data
        reminder_count = 0
        for anime in schedule_data:
            anime_title = anime["title"]
            anime_url = anime.get("url", "No URL provided")
            for ep in anime["schedule"]:
                try:
                    # Parse the time string in user's local timezone
                    airing_time = user_timezone.localize(datetime.strptime(ep["airingTime"], "%d/%m/%Y %H:%M"))
                    
                    # Set reminder time based on user preference
                    if use_delay:
                        reminder_time = airing_time + timedelta(hours=2)  # 2 hour delay
                    else:
                        reminder_time = airing_time  # No delay
                    
                    # Save to MongoDB with user_id (converted to UTC for storage)
                    reminders_collection.update_one(
                        {"title": anime_title, "episode": ep["episode"], "user_id": user_id},
                        {"$set": {
                            "title": anime_title,
                            "url": anime_url,
                            "episode": ep["episode"],
                            "airing_time": airing_time.astimezone(pytz.UTC),
                            "reminder_time": reminder_time.astimezone(pytz.UTC),
                            "user_id": user_id
                        }},
                        upsert=True
                    )
                    reminder_count += 1
                except ValueError:
                    await update.message.reply_text(
                        f"Invalid date format for {anime_title} Episode {ep['episode']}."
                    )
                    continue

        # Include a note about the delay setting in the confirmation message
        delay_note = "Reminders will be sent 2 hours after airing time." if use_delay else "Reminders will be sent exactly at airing time."
        timezone_note = f"Using your timezone: {user_timezone.zone}"
        await update.message.reply_text(
            f"Schedule added successfully! Set {reminder_count} anime reminders.\n\n"
            f"{delay_note}\n"
            f"{timezone_note}\n\n"
            f"To change delay setting, use /delay command.\n"
            f"To change timezone, use /timezone command."
        )
    except Exception as e:
        logger.error(f"Error in handle_schedule_json: {str(e)}")
        await update.message.reply_text(f"An error occurred: {str(e)}")
    return ConversationHandler.END

# Command: /show
async def show_reminders(update: Update, context: ContextTypes.DEFAULT_TYPE):
    # Register the user
    await register_user(update)
    
    user_id = update.effective_user.id
    
    # Get user's timezone
    user_timezone = await get_user_timezone(user_id)
    
    # Query reminders specific to this user
    if reminders_collection.count_documents({"user_id": user_id}) == 0:
        await update.message.reply_text("You don't have any reminders set.")
        return

    reminders = reminders_collection.find({"user_id": user_id})
    response = f"Your anime reminders (Timezone: {user_timezone.zone}):\n\n"
    for reminder in reminders:
        # Format anime reminders
        airing_time = reminder['airing_time'].astimezone(user_timezone).strftime("%d/%m/%Y %H:%M")
        response += f"{reminder['title']} Episode {reminder['episode']} - Airing: {airing_time}\n"
        
        if len(response) > 4000:  # Split long messages
            await context.bot.send_message(user_id, response)
            response = ""

    if response:
        await context.bot.send_message(user_id, response)

# Command: /next
async def next_reminders(update: Update, context: ContextTypes.DEFAULT_TYPE):
    # Register the user
    await register_user(update)
    
    user_id = update.effective_user.id
    
    # Get user's timezone
    user_timezone = await get_user_timezone(user_id)
    
    now = datetime.utcnow().replace(tzinfo=pytz.UTC)
    
    # Query upcoming reminders for this user
    upcoming_reminders = reminders_collection.find(
        {"user_id": user_id, "reminder_time": {"$gte": now}}
    ).sort("reminder_time", 1).limit(5)

    if reminders_collection.count_documents({"user_id": user_id, "reminder_time": {"$gte": now}}) == 0:
        await update.message.reply_text("You don't have any upcoming reminders.")
        return

    response = f"Your next 5 anime reminders (Timezone: {user_timezone.zone}):\n\n"
    for reminder in upcoming_reminders:
        # Format anime reminders with the user's timezone
        airing_time = reminder['airing_time'].astimezone(user_timezone).strftime("%d/%m/%Y %H:%M")
        response += f"{reminder['title']} Episode {reminder['episode']} - Airing: {airing_time}\n"

    await update.message.reply_text(response)

# Command: /remove
async def remove_schedule(update: Update, context: ContextTypes.DEFAULT_TYPE):
    # Register the user
    await register_user(update)

    user_id = update.effective_user.id
    now = datetime.utcnow().replace(tzinfo=pytz.UTC)
    
    # Get all reminders for this user
    reminders = list(reminders_collection.find({"user_id": user_id}))
    
    if not reminders:
        await update.message.reply_text("You don't have any reminders to remove.")
        return ConversationHandler.END
    
    # Group reminders by anime title
    anime_reminders = {}
    for reminder in reminders:
        title = reminder['title']
        if title not in anime_reminders:
            anime_reminders[title] = []
        anime_reminders[title].append(reminder)
    
    # Store the reminders in user_data for later use
    context.user_data["anime_reminders"] = anime_reminders
    context.user_data["anime_titles"] = list(anime_reminders.keys())
    
    # Create a numbered list of anime titles
    response = "Select the anime to remove by sending its number:\n\n"
    for i, title in enumerate(context.user_data["anime_titles"], 1):
        # Count episodes for this anime
        episode_count = len(anime_reminders[title])
        response += f"{i}. {title} ({episode_count} episode{'s' if episode_count > 1 else ''})\n"
    
    response += "\nOr send 'cancel' to cancel the removal."
    
    await update.message.reply_text(response)
    return REMOVE_TITLE

async def process_removal(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_response = update.message.text.strip().lower()
    
    # Check if user wants to cancel
    if user_response == 'cancel':
        await update.message.reply_text("Removal cancelled.")
        return ConversationHandler.END
    
    try:
        # Try to convert to integer for selection
        selection = int(user_response)
        
        # Validate selection
        if not context.user_data.get("anime_titles") or selection < 1 or selection > len(context.user_data["anime_titles"]):
            await update.message.reply_text("Invalid selection. Please try again or send 'cancel'.")
            return REMOVE_TITLE
        
        # Get the selected anime title
        title = context.user_data["anime_titles"][selection - 1]
        
        # Ask for confirmation
        await update.message.reply_text(
            f"Are you sure you want to remove all reminders for '{title}'?\n\n"
            f"Send 'yes' to confirm or 'no' to cancel."
        )
        
        # Store the selected title for confirmation
        context.user_data["selected_title"] = title
        
        return REMOVE_CONFIRMATION
        
    except ValueError:
        await update.message.reply_text("Please enter a valid number or 'cancel'.")
        return REMOVE_TITLE

async def confirm_removal(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_response = update.message.text.strip().lower()
    
    if user_response not in ['yes', 'no']:
        await update.message.reply_text("Please send 'yes' to confirm or 'no' to cancel.")
        return REMOVE_CONFIRMATION
    
    if user_response == 'no':
        await update.message.reply_text("Removal cancelled.")
        return ConversationHandler.END
    
    # User confirmed, proceed with removal
    title = context.user_data.get("selected_title")
    user_id = update.effective_user.id
    
    if not title:
        await update.message.reply_text("Something went wrong. Please try again.")
        return ConversationHandler.END
    
    # Remove all reminders for this anime title
    result = reminders_collection.delete_many({"title": title, "user_id": user_id})
    
    if result.deleted_count > 0:
        await update.message.reply_text(
            f"Removed {result.deleted_count} reminder{'s' if result.deleted_count > 1 else ''} for {title}."
        )
    else:
        await update.message.reply_text("No reminders found for the specified title.")
    
    # Clean up user_data
    if "anime_reminders" in context.user_data:
        del context.user_data["anime_reminders"]
    if "anime_titles" in context.user_data:
        del context.user_data["anime_titles"]
    if "selected_title" in context.user_data:
        del context.user_data["selected_title"]
    
    return ConversationHandler.END

# Add these helper functions after the imports
def parse_time(time_str: str) -> tuple[int, int]:
    """Parse time string in various formats (HH:MM, HHMM, H:MM, HMM)"""
    # Remove any spaces and convert to 24-hour format
    time_str = time_str.replace(" ", "").lower()
    
    if ':' in time_str:
        hour, minute = map(int, time_str.split(':'))
    else:
        # Handle formats like "2359" or "930"
        time_str = time_str.zfill(4)  # Pad with zeros if needed
        hour = int(time_str[:-2])
        minute = int(time_str[-2:])
    
    return hour, minute

def parse_date(date_str: str) -> tuple[int, int, int]:
    """Parse date string in various formats (DD/MM/YYYY, DD-MM-YYYY, DDMMYYYY, DD.MM.YYYY)"""
    # Remove any spaces and standardize separators
    date_str = date_str.replace(" ", "").replace("-", "/").replace(".", "/")
    
    if '/' in date_str:
        day, month, year = map(int, date_str.split('/'))
    else:
        # Handle format like "01042025"
        day = int(date_str[:2])
        month = int(date_str[2:4])
        year = int(date_str[4:])
        
    # Handle 2-digit years
    if year < 100:
        year += 2000 if year < 50 else 1900
        
    return day, month, year

# Parse relative date shortcuts like "today", "tomorrow", "day_after_tomorrow"
def parse_relative_date(date_str: str) -> datetime:
    """Convert relative date strings to datetime objects"""
    today = datetime.now(IST).replace(hour=0, minute=0, second=0, microsecond=0)
    
    if date_str.lower() in ["today", "td"]:
        return today
    elif date_str.lower() in ["tomorrow", "tom", "tmr"]:
        return today + timedelta(days=1)
    elif date_str.lower() in ["day_after_tomorrow", "dat"]:
        return today + timedelta(days=2)
    else:
        raise ValueError("Unknown relative date")

# Update the add_custom_reminder function
async def add_custom_reminder(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if not await is_owner(update):
        await update.message.reply_text("You are not authorized to use this bot.")
        return
    
    # Get the command arguments
    if not context.args or len(context.args) < 2:
        await update.message.reply_text(
            "Please use the format: /add TIME DATE Task description\n\n"
            "Supported formats:\n"
            "TIME: 23:59, 2359, 9:30, 930\n"
            "DATE: 01/04/2025, 01-04-2025, 01.04.2025, 01042025\n"
            "OR use shortcuts: today/td, tomorrow/tom/tmr, day_after_tomorrow/dat\n\n"
            "Examples:\n"
            "/add 2330 01042025 Do homework\n"
            "/add 14:30 tomorrow Team meeting\n"
            "/add 9:00 today Morning call"
        )
        return
    
    try:
        # Extract time
        time_str = context.args[0]
        try:
            hour, minute = parse_time(time_str)
        except (ValueError, IndexError):
            await update.message.reply_text(
                "Invalid time format. Examples: 23:59, 2359, 9:30, 930"
            )
            return
        
        # Extract date and check if it's a relative date shortcut
        date_str = context.args[1].lower()
        reminder_datetime = None
        
        if date_str in ["today", "td", "tomorrow", "tom", "tmr", "day_after_tomorrow", "dat"]:
            # Handle relative date
            try:
                base_date = parse_relative_date(date_str)
                reminder_datetime = base_date.replace(hour=hour, minute=minute)
            except ValueError:
                await update.message.reply_text("Invalid relative date format.")
                return
                
            # Extract task (everything after time and relative date)
            task = ' '.join(context.args[2:])
        else:
            # Handle standard date format
            try:
                day, month, year = parse_date(date_str)
                # Create datetime object in IST
                reminder_datetime = IST.localize(datetime(year, month, day, hour, minute))
                # Extract task (everything after time and date)
                task = ' '.join(context.args[2:])
            except (ValueError, IndexError):
                await update.message.reply_text(
                    "Invalid date format. Examples: 01/04/2025, 01-04-2025, 01.04.2025, 01042025"
                )
                return
        
        # Check if task is provided
        if not task:
            await update.message.reply_text("Please provide a task description.")
            return
            
        # Check if the date is valid and in the future
        now = datetime.now(IST)
        if reminder_datetime <= now:
            await update.message.reply_text("Reminder time must be in the future.")
            return
        
        # Store the reminder in MongoDB
        reminders_collection.insert_one({
            "title": "Custom Reminder",
            "task": task,
            "episode": None,
            "url": None,
            "airing_time": reminder_datetime.astimezone(pytz.UTC),
            "reminder_time": reminder_datetime.astimezone(pytz.UTC),
            "is_custom": True
        })
        
        # Format the response with the local time
        local_time = reminder_datetime.strftime("%d/%m/%Y %H:%M")
        await update.message.reply_text(f"✅ Custom reminder set for {local_time} IST: \"{task}\"")
        
    except Exception as e:
        logger.error(f"Error setting custom reminder: {str(e)}")
        await update.message.reply_text(
            "Failed to set reminder. Please try again with format: /add TIME DATE Task\n"
            "Examples:\n"
            "/add 2330 01042025 Do homework\n"
            "/add 14:30 tomorrow Team meeting"
        )

# Admin Commands

# Command: /usercount - Show total number of users
async def user_count(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if not await is_owner(update):
        await update.message.reply_text("This command is only available to the admin.")
        return
        
    count = await get_user_count()
    await update.message.reply_text(f"Total number of users: {count}")

# Command: /broadcast - Broadcast a message to all users
async def broadcast(update: Update, context: ContextTypes.DEFAULT_TYPE):
    if not await is_owner(update):
        await update.message.reply_text("This command is only available to the admin.")
        return
        
    # Check if the command is a reply to a message
    if not update.message.reply_to_message:
        await update.message.reply_text(
            "This command must be used as a reply to the message you want to broadcast."
        )
        return
        
    # Get the message to broadcast
    broadcast_msg = update.message.reply_to_message
    
    # Get all users
    users = users_collection.find({})
    success_count = 0
    fail_count = 0
    
    # Send status message
    status_msg = await update.message.reply_text("Broadcasting message...")
    
    # Broadcast to all users
    for user in users:
        user_id = user.get("user_id")
        try:
            # If the original message has text
            if broadcast_msg.text:
                await context.bot.send_message(
                    chat_id=user_id,
                    text=broadcast_msg.text
                )
            # If the original message has a photo
            elif broadcast_msg.photo:
                await context.bot.send_photo(
                    chat_id=user_id,
                    photo=broadcast_msg.photo[-1].file_id,
                    caption=broadcast_msg.caption
                )
            # If the original message has a document
            elif broadcast_msg.document:
                await context.bot.send_document(
                    chat_id=user_id,
                    document=broadcast_msg.document.file_id,
                    caption=broadcast_msg.caption
                )
            # If the original message has a video
            elif broadcast_msg.video:
                await context.bot.send_video(
                    chat_id=user_id,
                    video=broadcast_msg.video.file_id,
                    caption=broadcast_msg.caption
                )
            success_count += 1
        except Exception as e:
            logger.error(f"Failed to send broadcast to user {user_id}: {str(e)}")
            fail_count += 1
            
    # Update status message with results
    await status_msg.edit_text(
        f"Broadcast complete!\n"
        f"✅ Successfully sent to: {success_count} users\n"
        f"❌ Failed to send to: {fail_count} users"
    )

# Command: /search - Search for anime by title
async def search_anime(update: Update, context: ContextTypes.DEFAULT_TYPE):
    # Register the user
    await register_user(update)
    
    if not context.args:
        await update.message.reply_text(
            "Please provide an anime title to search for.\n"
            "Example: /search Komi Can't Communicate"
        )
        return ConversationHandler.END
        
    search_query = " ".join(context.args)
    user_id = update.effective_user.id
    
    await update.message.reply_text(f"Searching for anime: {search_query}...")
    
    # AniList GraphQL query
    query = """
    query ($search: String) {
        Page(perPage: 10) {
            media(type: ANIME, search: $search, sort: POPULARITY_DESC) {
                id
                title {
                    romaji
                    english
                }
                episodes
                status
                season
                seasonYear
                airingSchedule {
                    nodes {
                        episode
                        airingAt
                    }
                }
            }
        }
    }
    """
    
    variables = {
        "search": search_query
    }
    
    try:
        # Make request to AniList API using the global http_session
        if http_session:
            response = await http_session.post(
                "https://graphql.anilist.co",
                json={"query": query, "variables": variables}
            )
            data = await response.json()
        else:
            # Fallback to creating a temporary session if global session isn't available
            async with aiohttp.ClientSession() as session:
                response = await session.post(
                    "https://graphql.anilist.co",
                    json={"query": query, "variables": variables}
                )
                data = await response.json()
        
        if "errors" in data:
            await update.message.reply_text(f"Error searching for anime: {data['errors'][0]['message']}")
            return ConversationHandler.END
            
        results = data["data"]["Page"]["media"]
        
        if not results:
            await update.message.reply_text("No anime found with that title. Please try another search.")
            return ConversationHandler.END
        
        # Store results in user_data for later use
        context.user_data["search_results"] = results
        
        # Display results with numbers for selection
        response_text = "Please select an anime by sending its number:\n\n"
        
        for i, anime in enumerate(results, 1):
            title = anime["title"]["english"] or anime["title"]["romaji"]
            status = anime["status"]
            
            # Format the season and year if available
            season_info = ""
            if anime["season"] and anime["seasonYear"]:
                season_info = f"{anime['season'].capitalize()} {anime['seasonYear']}"
            
            # Format airing status
            if status == "RELEASING":
                status_text = "🟢 AIRING"
            elif status == "FINISHED":
                status_text = "🔵 FINISHED"
            elif status == "NOT_YET_RELEASED":
                status_text = "🟡 UPCOMING"
            else:
                status_text = status
            
            # Add number of airing episodes if available
            airing_info = ""
            if anime["airingSchedule"]["nodes"]:
                airing_episodes = len(anime["airingSchedule"]["nodes"])
                airing_info = f", {airing_episodes} scheduled episodes"
            
            response_text += f"{i}. {title} - {status_text} {season_info}{airing_info}\n"
        
        response_text += "\nOr send 'cancel' to cancel the search."
        
        await update.message.reply_text(response_text)
        return SEARCH_RESULT_SELECTION
        
    except Exception as e:
        logger.error(f"Error searching anime: {str(e)}")
        await update.message.reply_text("An error occurred while searching. Please try again later.")
        return ConversationHandler.END

# Handle anime selection from search results
async def handle_anime_selection(update: Update, context: ContextTypes.DEFAULT_TYPE):
    user_response = update.message.text.strip().lower()
    
    # Check if user wants to cancel
    if user_response == 'cancel':
        await update.message.reply_text("Search cancelled.")
        return ConversationHandler.END
    
    try:
        # Try to convert to integer for selection
        selection = int(user_response)
        
        # Check if selection is valid
        if not context.user_data.get("search_results") or selection < 1 or selection > len(context.user_data["search_results"]):
            await update.message.reply_text("Invalid selection. Please try again or send 'cancel'.")
            return SEARCH_RESULT_SELECTION
        
        # Get the selected anime
        selected_anime = context.user_data["search_results"][selection - 1]
        title = selected_anime["title"]["english"] or selected_anime["title"]["romaji"]
        
        # Check if there are airing episodes
        if not selected_anime["airingSchedule"]["nodes"]:
            await update.message.reply_text(
                f"No airing schedule found for {title}. "
                "This anime might be completed or doesn't have a confirmed airing schedule yet."
            )
            return ConversationHandler.END
        
        # Get user ID
        user_id = update.effective_user.id
        
        # Format and store reminders for this anime
        reminder_count = 0
        for episode in selected_anime["airingSchedule"]["nodes"]:
            airing_time = datetime.fromtimestamp(episode["airingAt"], tz=pytz.UTC)
            reminder_time = airing_time + timedelta(hours=2)  # Reminder 2 hours after airing
            
            # Save to MongoDB with user_id
            reminders_collection.update_one(
                {"title": title, "episode": episode["episode"], "user_id": user_id},
                {"$set": {
                    "title": title,
                    "url": f"https://anilist.co/anime/{selected_anime['id']}",
                    "episode": episode["episode"],
                    "airing_time": airing_time,
                    "reminder_time": reminder_time,
                    "user_id": user_id
                }},
                upsert=True
            )
            reminder_count += 1
        
        # Send confirmation
        await update.message.reply_text(
            f"Successfully added {title} to your reminders!\n"
            f"Set {reminder_count} episode reminder(s).\n\n"
            f"Use /next to see your upcoming reminders."
        )
        
        # Clear search results from user_data
        if "search_results" in context.user_data:
            del context.user_data["search_results"]
            
        return ConversationHandler.END
        
    except ValueError:
        await update.message.reply_text("Please enter a valid number or 'cancel'.")
        return SEARCH_RESULT_SELECTION
    except Exception as e:
        logger.error(f"Error handling anime selection: {str(e)}")
        await update.message.reply_text("An error occurred. Please try again later.")
        return ConversationHandler.END

# Command: /trending - Show currently airing and upcoming anime
async def trending_anime(update: Update, context: ContextTypes.DEFAULT_TYPE):
    # Register the user
    await register_user(update)
    
    await update.message.reply_text("Fetching currently airing and upcoming anime...")
    
    # AniList GraphQL query for trending anime
    query = """
    query {
        # Get currently airing anime
        airing: Page(page: 1, perPage: 10) {
            media(type: ANIME, status: RELEASING, sort: TRENDING_DESC) {
                id
                title {
                    romaji
                    english
                }
                status
                season
                seasonYear
                episodes
                nextAiringEpisode {
                    episode
                    airingAt
                }
            }
        }
        # Get upcoming anime
        upcoming: Page(page: 1, perPage: 10) {
            media(type: ANIME, status: NOT_YET_RELEASED, sort: POPULARITY_DESC) {
                id
                title {
                    romaji
                    english
                }
                status
                season
                seasonYear
                episodes
                nextAiringEpisode {
                    episode
                    airingAt
                }
            }
        }
    }
    """
    
    try:
        # Make request to AniList API using the global http_session
        if http_session:
            response = await http_session.post(
                "https://graphql.anilist.co",
                json={"query": query}
            )
            data = await response.json()
        else:
            # Fallback to creating a temporary session if global session isn't available
            async with aiohttp.ClientSession() as session:
                response = await session.post(
                    "https://graphql.anilist.co",
                    json={"query": query}
                )
                data = await response.json()
        
        if "errors" in data:
            await update.message.reply_text(f"Error fetching anime: {data['errors'][0]['message']}")
            return
            
        airing_results = data["data"]["airing"]["media"]
        upcoming_results = data["data"]["upcoming"]["media"]
        
        # Format the results
        response_text = "📺 *Currently Airing Anime*\n\n"
        
        if airing_results:
            for i, anime in enumerate(airing_results, 1):
                title = anime["title"]["english"] or anime["title"]["romaji"]
                season_info = f"{anime['season'].capitalize()} {anime['seasonYear']}" if anime['season'] and anime['seasonYear'] else ""
                
                next_ep_info = ""
                if anime["nextAiringEpisode"]:
                    next_ep = anime["nextAiringEpisode"]["episode"]
                    airing_time = datetime.fromtimestamp(anime["nextAiringEpisode"]["airingAt"], tz=pytz.UTC).astimezone(IST)
                    next_ep_info = f"Ep {next_ep} on {airing_time.strftime('%d %b, %H:%M')} IST"
                
                response_text += f"{i}. *{title}* - {season_info}\n"
                if next_ep_info:
                    response_text += f"   {next_ep_info}\n"
                response_text += f"   `/search {title}`\n\n"
        else:
            response_text += "No currently airing anime found.\n\n"
        
        # Send airing anime message
        await update.message.reply_text(response_text, parse_mode="Markdown")
        
        # Prepare upcoming anime message
        upcoming_text = "🔜 *Upcoming Anime*\n\n"
        
        if upcoming_results:
            for i, anime in enumerate(upcoming_results, 1):
                title = anime["title"]["english"] or anime["title"]["romaji"]
                season_info = f"{anime['season'].capitalize()} {anime['seasonYear']}" if anime['season'] and anime['seasonYear'] else ""
                
                airing_info = ""
                if anime["nextAiringEpisode"]:
                    airing_time = datetime.fromtimestamp(anime["nextAiringEpisode"]["airingAt"], tz=pytz.UTC).astimezone(IST)
                    airing_info = f"Starts on {airing_time.strftime('%d %b %Y')}"
                
                upcoming_text += f"{i}. *{title}* - {season_info}\n"
                if airing_info:
                    upcoming_text += f"   {airing_info}\n"
                upcoming_text += f"   `/search {title}`\n\n"
        else:
            upcoming_text += "No upcoming anime found.\n\n"
            
        # Send upcoming anime message
        await update.message.reply_text(upcoming_text, parse_mode="Markdown")
        
        # Add a help message about how to add these anime
        help_text = "To add any of these anime to your reminder list, click on the search command under the anime title or use:\n`/search anime title`"
        await update.message.reply_text(help_text, parse_mode="Markdown")
        
    except Exception as e:
        logger.error(f"Error fetching trending anime: {str(e)}")
        await update.message.reply_text("An error occurred while fetching anime data. Please try again later.")

# Command to toggle the 2-hour reminder delay
async def toggle_delay(update: Update, context: ContextTypes.DEFAULT_TYPE):
    # Register the user if not registered
    if not await register_user(update):
        await update.message.reply_text("An error occurred. Please try again.")
        return
    
    user_id = update.effective_user.id
    
    # Get current user settings
    user = users_collection.find_one({"user_id": user_id})
    
    if not user:
        await update.message.reply_text("An error occurred. Please try again.")
        return
    
    # Toggle the delay setting - using the helper function
    current_setting = to_boolean(user.get("use_delay", True))
    new_setting = not current_setting
    
    # Debug log to see what's happening
    logger.info(f"Toggle delay for user {user_id}: Current setting: {current_setting} (type: {type(current_setting)}), New setting: {new_setting} (type: {type(new_setting)})")
    
    try:
        # Update user preference in database - ensure boolean type
        result = users_collection.update_one(
            {"user_id": user_id},
            {"$set": {"use_delay": new_setting}}
        )
        
        # Log the update result
        logger.info(f"Database update result: matched={result.matched_count}, modified={result.modified_count}")
        
        if result.matched_count == 0:
            logger.error(f"Failed to update delay setting for user {user_id}: No matching document found")
            await update.message.reply_text("Failed to update your delay setting. Please try again.")
            return
            
        # Verify the update by re-fetching the user document
        updated_user = users_collection.find_one({"user_id": user_id})
        actual_setting = to_boolean(updated_user.get("use_delay")) if updated_user else None
        logger.info(f"After update, setting is now: {actual_setting} (type: {type(actual_setting)})")
        
        # Display message based on the ACTUAL current setting after update
        if actual_setting:
            await update.message.reply_text(
                "✅ Reminder delay is now ON.\n\n"
                "You will receive reminders 2 hours AFTER episodes start airing.\n"
                "This is useful to make sure episodes are available for streaming."
            )
        else:
            await update.message.reply_text(
                "❌ Reminder delay is now OFF.\n\n"
                "You will receive reminders EXACTLY at the airing time.\n"
                "Note: Episodes may not be available for streaming immediately."
            )
    except Exception as e:
        logger.error(f"Error toggling delay setting: {str(e)}")
        await update.message.reply_text("An error occurred while updating your preference. Please try again.")

# Handle timezone region selection via callback query
async def process_timezone_region_selection(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Process the selection of a timezone region from inline keyboard"""
    query = update.callback_query
    await query.answer()
    
    # Do NOT try to remove the keyboard first as it causes "message not modified" errors
    
    # Extract the region from callback data (format: "region_REGIONNAME")
    region = query.data.split("_")[1] if query.data.startswith("region_") else None
    
    if not region:
        await query.edit_message_text("An error occurred. Please try again with /timezone.")
        return ConversationHandler.END
    
    # Store the selected region in user_data
    context.user_data["selected_region"] = region
    
    # Get user's current timezone
    user_id = update.effective_user.id
    user = users_collection.find_one({"user_id": user_id})
    current_tz = user.get("timezone", "Asia/Kolkata") if user else "Asia/Kolkata"
    
    # Get timezones for the selected region
    common_timezones = context.user_data.get("timezones", {})
    if not common_timezones:
        # If timezones are not in user_data, recreate them
        common_timezones = {
            "Asia": ["Asia/Kolkata", "Asia/Tokyo", "Asia/Singapore", "Asia/Dubai", "Asia/Bangkok"],
            "Europe": ["Europe/London", "Europe/Paris", "Europe/Berlin", "Europe/Moscow"],
            "America": ["America/New_York", "America/Chicago", "America/Denver", "America/Los_Angeles"],
            "Australia": ["Australia/Sydney", "Australia/Perth"],
            "Africa": ["Africa/Cairo", "Africa/Johannesburg"],
            "Pacific": ["Pacific/Auckland", "Pacific/Honolulu"]
        }
        context.user_data["timezones"] = common_timezones
    
    timezones = common_timezones.get(region, [])
    if not timezones:
        await query.edit_message_text(f"No timezones found for region {region}. Please try again with /timezone.")
        return ConversationHandler.END
    
    # Create inline keyboard for timezone selection
    keyboard = []
    for i, tz in enumerate(timezones, 1):
        # Get current time in this timezone
        now = datetime.now(pytz.timezone(tz))
        time_str = now.strftime("%H:%M")
        
        # Add button for each timezone with the format "timezone_NUMBER"
        keyboard.append([InlineKeyboardButton(
            f"{i}. {tz} (Current time: {time_str})",
            callback_data=f"timezone_{i}"
        )])
    
    # Add a back button
    keyboard.append([InlineKeyboardButton("Back to regions", callback_data="back_to_regions")])
    reply_markup = InlineKeyboardMarkup(keyboard)
    
    # Send a completely new message to avoid "message not modified" errors
    try:
        # Use a message that is definitely different from the original
        await context.bot.send_message(
            chat_id=query.message.chat_id,
            text=f"🌍 Available timezones for {region.upper()}:\n\nYour current timezone: {current_tz}\n\nSelect a new timezone from the options below:",
            reply_markup=reply_markup
        )
        
        # Try to delete the original message to avoid confusion
        try:
            await context.bot.delete_message(
                chat_id=query.message.chat_id,
                message_id=query.message.message_id
            )
        except Exception as e:
            logger.warning(f"Could not delete original message: {e}")
            # If we can't delete, try to update with text that indicates it's outdated
            try:
                await query.edit_message_text(
                    f"Please check the new message with timezone options for {region}."
                )
            except Exception as nested_e:
                logger.warning(f"Could not update original message: {nested_e}")
    except Exception as e:
        logger.error(f"Error sending new timezone selection message: {e}")
        # Last resort - try to edit the message directly
        try:
            await query.edit_message_text(
                text=f"🌍 Available timezones for {region.upper()}:\n\nYour current timezone: {current_tz}\n\nSelect a new timezone from the options below:",
                reply_markup=reply_markup
            )
        except Exception as last_e:
            logger.error(f"Final attempt to update message failed: {last_e}")
            await query.message.reply_text("There was an error displaying timezone options. Please try using the /timezone command directly.")
    
    return TIMEZONE_SELECTION

# Handle timezone list selection from callbacks or text input
async def process_timezone_list_selection(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Process the selection of a timezone from the list"""
    
    # Handle different types of input (text message or callback query)
    if update.callback_query:
        # Handle selection from inline keyboard
        query = update.callback_query
        
        # Get the selection from user_data (set in button_handler)
        selection = context.user_data.get("timezone_selection")
        
        # Get selected region
        region = context.user_data.get("selected_region")
        if not region:
            await query.edit_message_text("An error occurred. Please try again with /timezone.")
            return ConversationHandler.END
            
        # Get timezones for this region
        timezones = context.user_data.get("timezones", {}).get(region, [])
        
        # Validate selection
        if not selection or selection < 1 or selection > len(timezones):
            await query.edit_message_text("Invalid selection. Please try again with /timezone.")
            return ConversationHandler.END
        
        # Get selected timezone
        timezone_str = timezones[selection - 1]
        user_id = update.effective_user.id
        
        # Get current timezone to check if this is a change
        user = users_collection.find_one({"user_id": user_id})
        current_tz = user.get("timezone", "Asia/Kolkata") if user else "Asia/Kolkata"
        changing_timezone = current_tz != timezone_str and user.get("timezone_set_manually", False)
        
        try:
            # Update user's timezone
            users_collection.update_one(
                {"user_id": user_id},
                {"$set": {
                    "timezone": timezone_str,
                    "timezone_set_manually": True  # Mark that user manually set their timezone
                }}
            )
            
            # Get current time in the selected timezone
            timezone = pytz.timezone(timezone_str)
            now = datetime.now(timezone)
            time_str = now.strftime("%d/%m/%Y %H:%M")
            
            # Different message if changing timezone vs setting for first time
            if changing_timezone:
                success_message = (
                    f"✅ Timezone changed from {current_tz} to {timezone_str}.\n"
                    f"Current time in your timezone: {time_str}\n\n"
                    "Your reminders will now be adjusted according to your new timezone."
                )
            else:
                success_message = (
                    f"✅ Timezone set to {timezone_str}.\n"
                    f"Current time in your timezone: {time_str}\n\n"
                    "Your reminders will now be adjusted according to your local time."
                )
            
            # Update message with success
            await query.edit_message_text(success_message)
            
            # Clean up user_data
            if "timezones" in context.user_data:
                del context.user_data["timezones"]
            if "regions" in context.user_data:
                del context.user_data["regions"]
            if "timezone_step" in context.user_data:
                del context.user_data["timezone_step"]
            if "selected_region" in context.user_data:
                del context.user_data["selected_region"]
            if "timezone_selection" in context.user_data:
                del context.user_data["timezone_selection"]
                
            return ConversationHandler.END
            
        except Exception as e:
            logger.error(f"Error setting timezone: {str(e)}")
            await query.edit_message_text("An error occurred. Please try again with /timezone.")
            return ConversationHandler.END
    else:
        # Handle selection from text message (original functionality)
        user_response = update.message.text.strip()
        
        try:
            selection = int(user_response)
            region = context.user_data.get("selected_region")
            timezones = context.user_data.get("timezones", {}).get(region, [])
            
            # Validate selection
            if selection < 1 or selection > len(timezones):
                await update.message.reply_text("Invalid selection. Please try again.")
                return TIMEZONE_SELECTION
            
            # Get selected timezone
            timezone_str = timezones[selection - 1]
            user_id = update.effective_user.id
            
            # Get current timezone to check if this is a change
            user = users_collection.find_one({"user_id": user_id})
            current_tz = user.get("timezone", "Asia/Kolkata") if user else "Asia/Kolkata"
            changing_timezone = current_tz != timezone_str and user.get("timezone_set_manually", False)
            
            # Update user's timezone
            users_collection.update_one(
                {"user_id": user_id},
                {"$set": {
                    "timezone": timezone_str,
                    "timezone_set_manually": True  # Mark that user manually set their timezone
                }}
            )
            
            # Get current time in the selected timezone
            timezone = pytz.timezone(timezone_str)
            now = datetime.now(timezone)
            time_str = now.strftime("%d/%m/%Y %H:%M")
            
            # Different message if changing timezone vs setting for first time
            if changing_timezone:
                success_message = (
                    f"✅ Timezone changed from {current_tz} to {timezone_str}.\n"
                    f"Current time in your timezone: {time_str}\n\n"
                    "Your reminders will now be adjusted according to your new timezone."
                )
            else:
                success_message = (
                    f"✅ Timezone set to {timezone_str}.\n"
                    f"Current time in your timezone: {time_str}\n\n"
                    "Your reminders will now be adjusted according to your local time."
                )
            
            await update.message.reply_text(success_message)
            
            # Clean up user_data
            if "timezones" in context.user_data:
                del context.user_data["timezones"]
            if "regions" in context.user_data:
                del context.user_data["regions"]
            if "timezone_step" in context.user_data:
                del context.user_data["timezone_step"]
            if "selected_region" in context.user_data:
                del context.user_data["selected_region"]
                
            return ConversationHandler.END
            
        except ValueError:
            await update.message.reply_text("Please enter a valid number.")
            return TIMEZONE_SELECTION
        except Exception as e:
            logger.error(f"Error in timezone selection: {str(e)}")
            await update.message.reply_text("An error occurred. Please try again.")
            return TIMEZONE_SELECTION

# Main Function
if __name__ == "__main__":
    while True:  # Continuous restart loop
        try:
            # Start Flask web server in a separate thread
            web_thread = Thread(target=run_web_server, daemon=True)
            web_thread.start()

            # Initialize bot application with better error handling
            app = ApplicationBuilder().token(BOT_TOKEN).build()
            
            # Create async helper functions
            async def create_http_session():
                global http_session
                http_session = aiohttp.ClientSession()
                
            async def close_http_session():
                global http_session
                if http_session and not http_session.closed:
                    await http_session.close()
                    http_session = None

            # Initialize HTTP session
            import asyncio
            loop = asyncio.get_event_loop()
            loop.run_until_complete(create_http_session())

            # Add ping job to keep connection alive
            async def ping_server(context: ContextTypes.DEFAULT_TYPE):
                try:
                    # Ping MongoDB to keep connection alive
                    db.command('ping')
                    logger.info("Ping successful - Bot is running")
                except Exception as e:
                    logger.error(f"Ping failed: {str(e)}")
                    
            # Run ping every 15 minutes
            app.job_queue.run_repeating(ping_server, interval=900, first=10)

            # Job Queue for reminders with error handling
            async def send_reminders_with_retry(context: ContextTypes.DEFAULT_TYPE):
                max_retries = 3
                retry_count = 0
                
                while retry_count < max_retries:
                    try:
                        await send_reminders(context)
                        break  # Success, exit retry loop
                    except (NetworkError, TimedOut, RetryAfter) as e:
                        retry_count += 1
                        logger.warning(f"Network error in send_reminders (attempt {retry_count}/{max_retries}): {str(e)}")
                        if retry_count < max_retries:
                            time.sleep(5)  # Wait before retry
                        else:
                            logger.error("Max retries reached for send_reminders")
                    except Exception as e:
                        logger.error(f"Unexpected error in send_reminders: {str(e)}")
                        break  # Don't retry for non-network errors
            
            app.job_queue.run_repeating(send_reminders_with_retry, interval=30, first=10)

            # Process missed reminders on startup with error handling
            async def process_missed_reminders_with_retry(context: ContextTypes.DEFAULT_TYPE):
                try:
                    await process_missed_reminders(context)
                except Exception as e:
                    logger.error(f"Error processing missed reminders: {str(e)}")
            
            app.job_queue.run_once(process_missed_reminders_with_retry, when=0)

            # Add handlers
            app.add_handler(CommandHandler("start", start))
            app.add_handler(CommandHandler("show", show_reminders))
            app.add_handler(CommandHandler("next", next_reminders))
            
            # Admin command handlers
            app.add_handler(CommandHandler("usercount", user_count))
            app.add_handler(CommandHandler("broadcast", broadcast))

            # Add search command handler (disabled - using external Oshi tool instead)
            # search_handler = ConversationHandler(
            #     entry_points=[CommandHandler("search", search_anime)],
            #     states={
            #         SEARCH_RESULT_SELECTION: [MessageHandler(filters.TEXT & ~filters.COMMAND, handle_anime_selection)],
            #     },
            #     fallbacks=[],
            # )
            # app.add_handler(search_handler)

            schedule_handler = ConversationHandler(
                entry_points=[CommandHandler("addschedule", add_schedule)],
                states={
                    ADD_SCHEDULE_JSON: [MessageHandler(filters.Document.ALL, handle_schedule_json)],
                },
                fallbacks=[],
            )
            app.add_handler(schedule_handler)

            remove_handler = ConversationHandler(
                entry_points=[CommandHandler("remove", remove_schedule)],
                states={
                    REMOVE_TITLE: [MessageHandler(filters.TEXT & ~filters.COMMAND, process_removal)],
                    REMOVE_CONFIRMATION: [MessageHandler(filters.TEXT & ~filters.COMMAND, confirm_removal)],
                },
                fallbacks=[],
            )
            app.add_handler(remove_handler)
            
            # Add trending command handler (disabled - using external Oshi tool instead)
            # app.add_handler(CommandHandler("trending", trending_anime))
            
            # Add delay toggle command
            app.add_handler(CommandHandler("delay", toggle_delay))
            
            # Add timezone command handler
            timezone_handler = ConversationHandler(
                entry_points=[CommandHandler("timezone", set_timezone)],
                states={
                    TIMEZONE_SELECTION: [
                        MessageHandler(filters.TEXT & ~filters.COMMAND, process_timezone_selection),
                        CallbackQueryHandler(button_handler)
                    ],
                },
                fallbacks=[],
            )
            app.add_handler(timezone_handler)
            
            # Add callback query handler for inline buttons
            app.add_handler(CallbackQueryHandler(button_handler))

            # Function to set up bot commands
            async def setup_commands(context: ContextTypes.DEFAULT_TYPE):
                commands = [
                    ("addschedule", "Add anime schedule from JSON file"),
                    ("show", "Show all your anime reminders"),
                    ("next", "Show your upcoming reminders"),
                    ("remove", "Remove anime reminders"),
                    ("delay", "Toggle 2-hour reminder delay"),
                    ("timezone", "Set your local timezone"),
                    ("start", "Show main menu")
                ]
                
                await context.application.bot.set_my_commands(commands)
            
            # Schedule the command setup as a job
            app.job_queue.run_once(setup_commands, when=1)

            # Add error handler for the application
            async def error_handler(update: object, context: ContextTypes.DEFAULT_TYPE) -> None:
                logger.error(f"Exception while handling an update: {context.error}")
                
            app.add_error_handler(error_handler)

            logger.info("Starting Reminder Bot...")
            app.run_polling(allowed_updates=Update.ALL_TYPES, drop_pending_updates=True)
            
        except Exception as e:
            logger.exception(f"Critical error occurred: {str(e)}")
            # Close HTTP session if it exists
            if http_session and not http_session.closed:
                try:
                    import asyncio
                    asyncio.run_coroutine_threadsafe(close_http_session(), asyncio.get_event_loop())
                except:
                    pass
            logger.info("Waiting 30 seconds before restarting...")
            time.sleep(30)  # Wait before restarting to prevent rapid restart loops